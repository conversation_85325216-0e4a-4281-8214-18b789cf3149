<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>True/False Scorecard Challenge</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --background: #0f172a;
            --surface: #1e293b;
            --surface-light: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --border: #475569;
            --shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-lg: 0 35px 60px -12px rgba(0, 0, 0, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--background);
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        .main-container {
            background: var(--surface);
            border: 2px solid var(--border);
            border-radius: 2rem;
            box-shadow: var(--shadow-lg);
            padding: 1rem;
            max-width: 1200px;
            width: 100%;
            position: relative;
            backdrop-filter: blur(20px);
        }

        @media (min-width: 768px) {
            .main-container {
                padding: 1rem;
                max-width: 1000px;
            }
        }

        @media (min-width: 1024px) {
            .main-container {
                padding: 2rem;
                max-width: 1200px;
                max-height: 1200px;
            }
        }

        .quiz-header-text {
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 800;
            margin-bottom: 2rem;
            text-align: center;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (min-width: 768px) {
            .quiz-header-text {
                font-size: 2.5rem;
            }
        }

        @media (min-width: 1024px) {
            .quiz-header-text {
                font-size: 3rem;
            }
        }

        .progress-bar-bg {
            background: var(--surface-light);
            border-radius: 1rem;
            height: 0.75rem;
            overflow: hidden;
            margin-bottom: 1rem;
            border: 1px solid var(--border);
        }

        .progress-bar-inner {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 1rem;
            box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
        }

        #card-stack {
            position: relative;
            width: 100%;
            min-height: 350px;
            margin: 2rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (min-width: 768px) {
            #card-stack {
                min-height: 400px;
            }
        }

        @media (min-width: 1024px) {
            #card-stack {
                min-height: 450px;
            }
        }

        .quiz-card {
            background: var(--surface);
            border: 2px solid var(--border);
            border-radius: 2rem;
            padding: 3rem;
            position: absolute;
            width: 100%;
            max-width: 700px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;
            opacity: 0;
            transform: scale(0.95) translateY(20px);
            transition: all 0.4s ease;
            pointer-events: none;
            gap: 1rem;
            box-shadow: var(--shadow-lg);
            min-height: 100px;
        }

        @media (min-width: 768px) {
            .quiz-card {
                padding: 1rem;
                gap: 1.5rem;
            }
        }

        @media (min-width: 1024px) {
            .quiz-card {
                padding: 2rem;
                gap: 3rem;
            }
        }

        .quiz-card.current {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
            z-index: 10;
        }

        .quiz-card h2 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
            line-height: 1.6;
            margin-bottom: 0;
            text-align: center;
            max-width: 100%;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (min-width: 768px) {
            .quiz-card h2 {
                font-size: 1.75rem;
            }
        }

        @media (min-width: 1024px) {
            .quiz-card h2 {
                font-size: 2rem;
            }
        }

        .answer-btn {
            background: var(--surface-light);
            color: var(--text-primary);
            border: 3px solid var(--border);
            border-radius: 1.5rem;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            flex-shrink: 0;
            width: 25%;
            text-transform: uppercase;
            letter-spacing: 0.2px;
        }

        @media (min-width: 768px) {
            .answer-btn {
                padding: 1.25rem 2.5rem;
                font-size: 1.2rem;
                min-width: 160px;
                width: 40%;
            }
        }

        @media (min-width: 1024px) {
            .answer-btn {
                padding: 1.5rem 3rem;
                font-size: 1.3rem;
                min-width: 180px;
                width: 35%;
            }
        }

        .answer-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .answer-btn.true {
            border-color: var(--success);
            color: var(--success);
            background: rgba(16, 185, 129, 0.1);
        }

        .answer-btn.true:hover {
            background: var(--success);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }

        .answer-btn.false {
            border-color: var(--danger);
            color: var(--danger);
            background: rgba(239, 68, 68, 0.1);
        }

        .answer-btn.false:hover {
            background: var(--danger);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
        }

        .answer-btn.correct {
            background: var(--success) !important;
            color: white !important;
            border-color: var(--success) !important;
            animation: pulse 0.6s ease;
        }

        .answer-btn.incorrect {
            background: var(--danger) !important;
            color: white !important;
            border-color: var(--danger) !important;
            animation: shake 0.6s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .answer-btn.disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        .sound-toggle {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            background: var(--surface-light);
            border: 1px solid var(--border);
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .sound-toggle:hover {
            background: var(--primary);
            color: white;
            transform: scale(1.1);
        }

        .streak-indicator {
            position: absolute;
            top: -1rem;
            right: -1rem;
            background: var(--warning);
            color: white;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
            animation: pulse 1s infinite;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            border-radius: 1rem;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }


        .timer-ring {
            position: absolute;
            top: 1rem;
            left: 1rem;
            width: 3rem;
            height: 3rem;
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background: var(--primary);
            animation: confetti-fall 3s ease-out forwards;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(400px) rotate(720deg);
                opacity: 0;
            }
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary);
            border-radius: 50%;
            pointer-events: none;
            animation: particle-float 20s linear infinite;
        }

        @keyframes particle-float {
            0% {
                transform: translateX(-100px) translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-50px) rotate(360deg);
                opacity: 0;
            }
        }

        #result-screen {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 2rem;
            padding: 3rem;
            text-align: center;
            box-shadow: var(--shadow-lg);
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.4s ease;
            pointer-events: none;
            max-width: 600px;
            width: 100%;
        }

        @media (min-width: 768px) {
            #result-screen {
                padding: 4rem;
                max-width: 700px;
            }
        }

        #result-screen.visible {
            transform: scale(1);
            opacity: 1;
            pointer-events: auto;
        }

        #result-screen h2 {
            color: var(--text-primary);
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
            #result-screen h2 {
                font-size: 3rem;
            }
        }

        #result-screen p {
            color: var(--text-secondary);
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            #result-screen p {
                font-size: 1.3rem;
            }
        }

        #score-text {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 2rem 0;
        }

        @media (min-width: 768px) {
            #score-text {
                font-size: 5rem;
            }
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <!-- Floating particles -->
    <div id="particles-container"></div>

    <div id="quiz-container" class="main-container w-full max-w-md mx-auto">
        <!-- Sound toggle -->
        <button id="sound-toggle" class="sound-toggle" title="Toggle Sound">
            <span id="sound-icon">🔊</span>
        </button>

        <!-- Timer ring (appears during questions) -->
        <div id="timer-ring" class="timer-ring" style="display: none;"></div>

        <!-- Quiz Header -->
        <div id="quiz-header" class="text-center mb-8">
            <h1 class="quiz-header-text text-4xl font-extrabold tracking-tight mb-6">True or False?</h1>
            
            <!-- Progress Section -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <span id="question-counter" class="text-sm font-medium text-gray-400">Question 1 of 10</span>
                    <div id="streak-container" style="position: relative; display: inline-block;">
                        <div id="streak-indicator" class="streak-indicator" style="display: none;">
                            <span id="streak-count">0</span>
                        </div>
                    </div>
                </div>
                <div class="w-full progress-bar-bg rounded-full h-3">
                    <div id="progress-bar" class="progress-bar-inner h-3 rounded-full" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- Card Stack -->
        <div id="card-stack">
            <!-- Cards will be generated by JS -->
        </div>

        <!-- Continue Button (shown after quiz completion) -->
        <div id="continue-container" class="text-center mt-6" style="display: none;">
            <button id="continue-btn" class="btn-primary">Continue</button>
        </div>
    </div>

    <!-- Result Screen -->
    <div id="result-screen" class="hidden">
        <h2>Challenge Complete!</h2>
        <p>Your Score:</p>
        <p id="score-text">8/10</p>
        
        <button id="continue-btn-result" class="btn-primary">Continue</button>
    </div>

    <script>
        const quizData = [
            { statement: "Before the scorecard, performance measurement was already holistic and multi-dimensional.", answer: false },
            { statement: "The 'D-minus one' logic means today's work appears on tomorrow's scorecard.", answer: true },
            { statement: "The scorecard was simplified over time, removing quality-driven parameters.", answer: false },
            { statement: "DRA certification percentage is a key compliance metric on the scorecard.", answer: true },
            { statement: "The scorecard only allows team members to see their performance at the end of the month.", answer: false },
            { statement: "Managers are expected to use the scorecard primarily for annual reviews, not daily coaching.", answer: false },
            { statement: "A core benefit of the scorecard is building a culture of transparency and excellence.", answer: true },
            { statement: "To ensure accuracy, all mappings for the scorecard are finalized by the 15th of the month.", answer: false },
            { statement: "The first version of the scorecard was designed as a daily KPI dashboard for field teams.", answer: true },
            { statement: "Joy's passion for the scorecard comes from its ability to drive organizational culture and values.", answer: true }
        ];

        // DOM Elements
        const quizContainer = document.getElementById('quiz-container');
        const cardStack = document.getElementById('card-stack');
        const progressBar = document.getElementById('progress-bar');
        const questionCounter = document.getElementById('question-counter');
        const resultScreen = document.getElementById('result-screen');
        const scoreText = document.getElementById('score-text');
        const continueBtn = document.getElementById('continue-btn');
        const continueBtnResult = document.getElementById('continue-btn-result');
        const continueContainer = document.getElementById('continue-container');
        const soundToggle = document.getElementById('sound-toggle');
        const soundIcon = document.getElementById('sound-icon');
        const streakIndicator = document.getElementById('streak-indicator');
        const streakCount = document.getElementById('streak-count');
        const timerRing = document.getElementById('timer-ring');
        const particlesContainer = document.getElementById('particles-container');

        // Game state
        let currentQuestionIndex = 0;
        let score = 0;
        let cards = [];
        let soundEnabled = true;
        let currentStreak = 0;
        let bestStreak = 0;
        let questionStartTime = 0;
        let responseTimes = [];

        // Sound functions (using Web Audio API for cross-browser compatibility)
        function createSound(frequency, duration, type = 'sine') {
            if (!soundEnabled) return;
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = frequency;
                oscillator.type = type;
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            } catch (e) {
                console.log('Audio not supported');
            }
        }

        function playCorrectSound() {
            createSound(800, 0.3);
            setTimeout(() => createSound(1000, 0.2), 100);
        }

        function playIncorrectSound() {
            createSound(300, 0.5, 'square');
        }

        function playCompleteSound() {
            createSound(523, 0.2); // C
            setTimeout(() => createSound(659, 0.2), 100); // E
            setTimeout(() => createSound(784, 0.2), 200); // G
            setTimeout(() => createSound(1047, 0.4), 300); // C
        }

        // Create floating particles
        function createParticles() {
            setInterval(() => {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                particlesContainer.appendChild(particle);
                
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.remove();
                    }
                }, 25000);
            }, 3000);
        }

        // Sound toggle functionality
        soundToggle.addEventListener('click', () => {
            soundEnabled = !soundEnabled;
            soundIcon.textContent = soundEnabled ? '🔊' : '🔇';
            if (soundEnabled) {
                playCorrectSound();
            }
        });

        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.backgroundColor = ['#6366f1', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444'][Math.floor(Math.random() * 5)];
                    confetti.style.animationDelay = Math.random() * 0.5 + 's';
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        if (confetti.parentNode) {
                            confetti.remove();
                        }
                    }, 3000);
                }, i * 50);
            }
        }

        function updateStreakIndicator() {
            if (currentStreak >= 2) {
                streakIndicator.style.display = 'flex';
                streakCount.textContent = currentStreak;
            } else {
                streakIndicator.style.display = 'none';
            }
            
            if (currentStreak > bestStreak) {
                bestStreak = currentStreak;
            }
        }

        function showMotivationalMessage() {
            if (currentStreak >= 3 && currentStreak % 2 === 0) {
                const messages = [
                    "🎯 You're on fire! Keep going!",
                    "🚀 Excellent work! You've got this!",
                    "⭐ Outstanding performance!",
                    "💪 You're crushing it!",
                    "🏆 Fantastic job so far!"
                ];
                
                const message = messages[Math.floor(Math.random() * messages.length)];
                const messageEl = document.createElement('div');
                messageEl.textContent = message;
                messageEl.style.cssText = `
                    position: fixed;
                    top: 20%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-weight: 600;
                    z-index: 1000;
                    animation: message-bounce 2s ease-out forwards;
                `;
                
                document.body.appendChild(messageEl);
                
                setTimeout(() => {
                    messageEl.style.opacity = '0';
                    messageEl.style.transform = 'translateX(-50%) translateY(-20px) scale(0.8)';
                    setTimeout(() => {
                        if (messageEl.parentNode) {
                            messageEl.remove();
                        }
                    }, 300);
                }, 1500);
            }
        }

        function startQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            currentStreak = 0;
            bestStreak = 0;
            responseTimes = [];
            
            resultScreen.classList.add('hidden');
            resultScreen.classList.remove('visible');
            quizContainer.style.display = 'block';
            continueContainer.style.display = 'none';

            cardStack.innerHTML = '';
            createCards();
            updateProgress();
            updateStreakIndicator();
            
            // Start timer for first question
            questionStartTime = Date.now();
            timerRing.style.display = 'block';
        }

        function createCards() {
            const shuffledQuizData = [...quizData].sort(() => Math.random() - 0.5);
            
            cards = shuffledQuizData.map((data) => {
                const card = document.createElement('div');
                card.classList.add('quiz-card');

                const statementText = document.createElement('h2');
                statementText.textContent = data.statement;

                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'flex gap-6 justify-center w-full';

                const trueBtn = document.createElement('button');
                trueBtn.textContent = 'True';
                trueBtn.className = 'answer-btn true';
                trueBtn.onclick = () => selectAnswer(true, data.answer, card);

                const falseBtn = document.createElement('button');
                falseBtn.textContent = 'False';
                falseBtn.className = 'answer-btn false';
                falseBtn.onclick = () => selectAnswer(false, data.answer, card);
                
                buttonContainer.appendChild(trueBtn);
                buttonContainer.appendChild(falseBtn);
                
                card.appendChild(statementText);
                card.appendChild(buttonContainer);
                cardStack.appendChild(card);
                return card;
            });
            
            positionCards();
        }
        
        function positionCards() {
            cards.forEach((card, index) => {
                card.classList.remove('current', 'next');
                if (index === currentQuestionIndex) {
                    card.classList.add('current');
                } else if (index === currentQuestionIndex + 1) {
                    card.classList.add('next');
                }
            });
        }

        function selectAnswer(userAnswer, correctAnswer, card) {
            // Record response time
            const responseTime = (Date.now() - questionStartTime) / 1000;
            responseTimes.push(responseTime);
            
            const isCorrect = userAnswer === correctAnswer;
            const buttons = card.querySelectorAll('.answer-btn');

            buttons.forEach(btn => {
                btn.classList.add('disabled');
                const btnAnswer = btn.textContent.toLowerCase() === 'true';
                if (btnAnswer === correctAnswer) {
                    btn.classList.add('correct');
                }
            });
            
            if (!isCorrect) {
                buttons.forEach(btn => {
                    const btnAnswer = btn.textContent.toLowerCase() === 'true';
                    if(btnAnswer === userAnswer) {
                        btn.classList.add('incorrect');
                    }
                });
            }

            // Update score and streak
            if (isCorrect) {
                score++;
                currentStreak++;
                playCorrectSound();
                showMotivationalMessage();
            } else {
                currentStreak = 0;
                playIncorrectSound();
            }
            
            updateStreakIndicator();
            
            // Fade out the current card
            card.classList.remove('current');

            setTimeout(() => {
                currentQuestionIndex++;
                if (currentQuestionIndex < cards.length) {
                    updateProgress();
                    positionCards();
                    questionStartTime = Date.now(); // Start timer for next question
                } else {
                    timerRing.style.display = 'none';
                    showResults();
                }
            }, 1000);
        }

        function updateProgress() {
            const progressPercentage = (currentQuestionIndex / cards.length) * 100;
            progressBar.style.width = `${progressPercentage}%`;
            questionCounter.textContent = `Question ${currentQuestionIndex + 1} / ${cards.length}`;
        }


        function showResults() {
            // Show continue button below card stack
            continueContainer.style.display = 'block';
            
            // Play completion sound and create confetti
            playCompleteSound();
            createConfetti();

            // Set variable for external integration (Storyline)
            try {
                const player = parent.GetPlayer();             
                if (player) {
                    player.SetVar("ContinueT", 1);
                    console.log("Variable set: ContinueT=1");
                }
                if(parent){
                    console.log("Parent object found.");
                }
            } catch (e) {
                console.error("Could not get player/parent and set variable:", e);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (currentQuestionIndex < cards.length) {
                const currentCard = cards[currentQuestionIndex];
                if (currentCard && currentCard.classList.contains('current')) {
                    const buttons = currentCard.querySelectorAll('.answer-btn:not(.disabled)');
                    if (e.key === '1' || e.key.toLowerCase() === 't') {
                        buttons[0]?.click(); // True button
                    } else if (e.key === '2' || e.key.toLowerCase() === 'f') {
                        buttons[1]?.click(); // False button
                    }
                }
            } else if (resultScreen.classList.contains('visible')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    continueBtn.click();
                }
            }
        });

        // Event listeners
        continueBtn.addEventListener('click', () => {
            startQuiz();
        });

        continueBtnResult.addEventListener('click', () => {
            startQuiz();
        });

        // Initialize particles and start quiz
        createParticles();
        startQuiz();
    </script>
</body>
</html>